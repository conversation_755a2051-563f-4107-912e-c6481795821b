#!/usr/bin/env python3
"""
Step-by-step example showing Mini-vLLM internals

This example demonstrates the internal workflow by using the engine directly
and showing each step of the generation process.
"""

import logging
from mini_vllm import MiniEngine, SamplingParams

# Set up detailed logging
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s - %(name)s - %(message)s')
logger = logging.getLogger(__name__)


def main():
    print("🔍 Mini-vLLM Step-by-Step Example")
    print("=" * 50)
    
    # Initialize the engine directly (lower-level than MiniLLM)
    print("\n🏗️  Step 1: Initialize Engine")
    print("-" * 30)
    
    engine = MiniEngine(
        model_name="gpt2",
        max_batch_size=2,
        max_seq_len=50
    )
    
    print(f"✅ Engine initialized with model: {engine.model_name}")
    
    # Add requests manually
    print("\n📝 Step 2: Add Requests")
    print("-" * 30)
    
    sampling_params = SamplingParams(
        temperature=0.8,
        max_tokens=20,
        top_p=0.9
    )
    
    prompts = [
        "The weather today is",
        "My favorite color is"
    ]
    
    request_ids = []
    for i, prompt in enumerate(prompts):
        request_id = engine.add_request(prompt, sampling_params)
        request_ids.append(request_id)
        print(f"✅ Added request {request_id}: '{prompt}'")
    
    # Show initial stats
    print(f"\n📊 Initial stats: {engine.get_stats()}")
    
    # Run the step loop manually
    print("\n⚙️  Step 3: Manual Step Loop")
    print("-" * 30)
    
    step_count = 0
    all_outputs = []
    
    while engine.has_unfinished_requests():
        step_count += 1
        print(f"\n🔄 Step {step_count}:")
        
        # Show what the scheduler is doing
        scheduler_stats = engine.scheduler.get_stats()
        print(f"  📋 Scheduler state: {scheduler_stats}")
        
        # Execute one step
        step_outputs = engine.step()
        
        # Show what happened
        if step_outputs:
            print(f"  ✅ Completed {len(step_outputs)} requests:")
            for output in step_outputs:
                print(f"    - {output.request_id}: '{output.text}'")
                all_outputs.extend(step_outputs)
        else:
            print(f"  ⏳ No requests completed this step")
        
        # Safety check to avoid infinite loops
        if step_count > 50:
            print("  ⚠️  Too many steps, breaking...")
            break
    
    print(f"\n🏁 Generation completed in {step_count} steps")
    
    # Show final results
    print("\n📋 Step 4: Final Results")
    print("-" * 30)
    
    for output in all_outputs:
        print(f"\nRequest: {output.request_id}")
        print(f"Prompt: {output.prompt}")
        print(f"Generated: {output.text}")
        print(f"Finished: {output.finished}")
        print(f"Finish reason: {output.finish_reason}")
    
    # Show final stats
    final_stats = engine.get_stats()
    print(f"\n📊 Final stats: {final_stats}")
    
    # Demonstrate request abortion
    print("\n🛑 Step 5: Request Abortion Demo")
    print("-" * 30)
    
    # Add a new request
    abort_request_id = engine.add_request("This request will be aborted", sampling_params)
    print(f"✅ Added request to abort: {abort_request_id}")
    
    # Abort it immediately
    success = engine.abort_request(abort_request_id)
    print(f"🛑 Abort request result: {success}")
    
    # Show that it's gone
    stats_after_abort = engine.get_stats()
    print(f"📊 Stats after abort: {stats_after_abort}")
    
    print("\n✅ Step-by-step example completed!")


def demonstrate_batching():
    """Demonstrate how batching works with multiple requests"""
    print("\n🔄 Batching Demonstration")
    print("=" * 50)
    
    # Create engine with small batch size to see batching in action
    engine = MiniEngine("gpt2", max_batch_size=2, max_seq_len=30)
    
    # Add multiple requests
    prompts = [
        "Hello",
        "World", 
        "How",
        "Are",
        "You"
    ]
    
    sampling_params = SamplingParams(temperature=0.8, max_tokens=10)
    
    print(f"Adding {len(prompts)} requests with batch_size=2:")
    for prompt in prompts:
        request_id = engine.add_request(prompt, sampling_params)
        print(f"  ✅ Added: '{prompt}' -> {request_id}")
    
    # Run and show batching behavior
    step = 0
    while engine.has_unfinished_requests():
        step += 1
        stats = engine.scheduler.get_stats()
        print(f"\nStep {step} - Waiting: {stats['waiting']}, Running: {stats['running']}")
        
        outputs = engine.step()
        if outputs:
            print(f"  Completed: {[o.request_id for o in outputs]}")
        
        if step > 20:  # Safety break
            break


if __name__ == "__main__":
    try:
        main()
        demonstrate_batching()
    except KeyboardInterrupt:
        print("\n\n⚠️  Interrupted by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
