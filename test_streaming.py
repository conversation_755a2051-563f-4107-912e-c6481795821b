#!/usr/bin/env python3
"""
Simple test for streaming functionality
"""
import asyncio
import httpx

async def test_streaming():
    """Test streaming endpoint"""
    url = "http://localhost:8000/v1/chat/completions"
    
    payload = {
        "model": "gpt2",
        "messages": [{"role": "user", "content": "Hello"}],
        "max_tokens": 10,
        "temperature": 0.7,
        "stream": True
    }
    
    print("Testing streaming...")
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        async with client.stream("POST", url, json=payload) as response:
            print(f"Response status: {response.status_code}")
            print(f"Response headers: {dict(response.headers)}")
            
            if response.status_code != 200:
                content = await response.aread()
                print(f"Error response: {content}")
                return
            
            print("Streaming content:")
            async for line in response.aiter_lines():
                print(f"Line: {repr(line)}")
                if line.startswith("data: [DONE]"):
                    break

if __name__ == "__main__":
    asyncio.run(test_streaming())
