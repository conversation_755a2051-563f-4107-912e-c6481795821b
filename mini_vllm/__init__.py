"""
Mini-vLLM: A simplified version of vLLM for educational purposes

This package provides a minimal implementation of the core vLLM architecture:
- LLM interface for easy usage
- Engine for request coordination  
- Scheduler for batching and queue management
- ModelRunner for inference using HuggingFace transformers
- Data structures for requests and responses

Key simplifications:
- Single GPU/CPU only (no distributed execution)
- Basic batching (no advanced memory management)
- HuggingFace transformers (no custom kernels)
- Simple scheduling (FIFO, no priorities)
- No streaming support
"""

from .llm import MiniLLM, generate_text
from .engine import MiniEngine
from .scheduler import MiniScheduler
from .model_runner import MiniModelRunner
from .data_structures import (
    SamplingParams,
    Request,
    RequestOutput,
    Sequence,
    SequenceGroup,
    SequenceStatus
)

__version__ = "0.1.0"
__all__ = [
    # Main user interface
    "MiniLLM",
    "generate_text",
    
    # Core components
    "MiniEngine", 
    "MiniScheduler",
    "MiniModelRunner",
    
    # Data structures
    "SamplingParams",
    "Request",
    "RequestOutput", 
    "Sequence",
    "SequenceGroup",
    "SequenceStatus"
]
