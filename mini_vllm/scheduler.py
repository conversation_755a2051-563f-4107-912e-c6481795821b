"""
Simple scheduler for Mini-vLLM
Manages request queues and batching decisions
"""
from typing import List, Optional
from collections import deque
import logging

from .data_structures import (
    SequenceGroup, SequenceStatus, SchedulerOutput, SamplingParams
)

logger = logging.getLogger(__name__)


class MiniScheduler:
    """
    Simplified scheduler that manages request queues and batching
    
    Key simplifications:
    - FIFO scheduling (no priorities)
    - Simple batching (just pack requests up to max_batch_size)
    - No memory management (no KV cache blocks)
    - No preemption or swapping
    """
    
    def __init__(self, max_batch_size: int = 4, max_seq_len: int = 512):
        self.max_batch_size = max_batch_size
        self.max_seq_len = max_seq_len
        
        # Request queues
        self.waiting: deque[SequenceGroup] = deque()  # New requests
        self.running: List[SequenceGroup] = []        # Currently processing
        self.finished: List[SequenceGroup] = []       # Completed requests
        
        logger.info(f"MiniScheduler initialized: max_batch_size={max_batch_size}, max_seq_len={max_seq_len}")
    
    def add_sequence_group(self, seq_group: SequenceGroup) -> None:
        """Add a new sequence group to the waiting queue"""
        logger.debug(f"Adding sequence group {seq_group.request_id} to waiting queue")
        self.waiting.append(seq_group)
    
    def schedule(self) -> SchedulerOutput:
        """
        Main scheduling logic - decide what to execute this step
        
        Returns:
            SchedulerOutput with sequences to process
        """
        scheduled_groups = []
        
        # Step 1: Continue processing running sequences (decode phase)
        decode_groups = self._schedule_decode()
        scheduled_groups.extend(decode_groups)
        
        # Step 2: Start new sequences if we have capacity (prefill phase)
        if len(scheduled_groups) < self.max_batch_size:
            prefill_groups = self._schedule_prefill(self.max_batch_size - len(scheduled_groups))
            scheduled_groups.extend(prefill_groups)
        
        # Step 3: Move finished sequences to completed queue
        self._update_finished()
        
        # Calculate total tokens in batch
        num_batched_tokens = sum(
            seq.get_len for group in scheduled_groups 
            for seq in group.sequences
        )
        
        logger.debug(f"Scheduled {len(scheduled_groups)} groups, {num_batched_tokens} tokens total")
        
        return SchedulerOutput(
            scheduled_groups=scheduled_groups,
            num_batched_tokens=num_batched_tokens
        )
    
    def _schedule_decode(self) -> List[SequenceGroup]:
        """Schedule running sequences for decode (continue generation)"""
        decode_groups = []
        
        for group in self.running:
            if not group.is_finished:
                # Check if sequence is getting too long
                main_seq = group.get_main_sequence()
                if main_seq.get_len >= self.max_seq_len:
                    # Mark as finished due to length limit
                    main_seq.status = SequenceStatus.FINISHED
                    logger.debug(f"Sequence {main_seq.seq_id} finished due to length limit")
                else:
                    decode_groups.append(group)
        
        return decode_groups
    
    def _schedule_prefill(self, available_slots: int) -> List[SequenceGroup]:
        """Schedule new sequences for prefill (start generation)"""
        prefill_groups = []
        
        while len(prefill_groups) < available_slots and self.waiting:
            group = self.waiting.popleft()
            
            # Move to running state
            for seq in group.sequences:
                seq.status = SequenceStatus.RUNNING
            
            self.running.append(group)
            prefill_groups.append(group)
            
            logger.debug(f"Started processing sequence group {group.request_id}")
        
        return prefill_groups
    
    def _update_finished(self) -> None:
        """Move finished sequences from running to finished queue"""
        still_running = []

        for group in self.running:
            if group.is_finished:
                self.finished.append(group)
                logger.debug(f"Sequence group {group.request_id} finished and moved to finished queue")
            else:
                still_running.append(group)

        self.running = still_running
        logger.debug(f"After update_finished: running={len(self.running)}, finished={len(self.finished)}")
    
    def has_unfinished_requests(self) -> bool:
        """Check if there are any unfinished requests"""
        return len(self.waiting) > 0 or len(self.running) > 0
    
    def get_finished_requests(self) -> List[SequenceGroup]:
        """Get and clear finished requests"""
        finished = self.finished.copy()
        logger.debug(f"Getting {len(finished)} finished requests")
        self.finished.clear()
        return finished
    
    def abort_request(self, request_id: str) -> bool:
        """Abort a request (remove from queues)"""
        # Remove from waiting queue
        for i, group in enumerate(self.waiting):
            if group.request_id == request_id:
                del self.waiting[i]
                logger.info(f"Aborted waiting request {request_id}")
                return True
        
        # Remove from running queue
        for i, group in enumerate(self.running):
            if group.request_id == request_id:
                del self.running[i]
                logger.info(f"Aborted running request {request_id}")
                return True
        
        return False
    
    def get_stats(self) -> dict:
        """Get scheduler statistics"""
        return {
            "waiting": len(self.waiting),
            "running": len(self.running),
            "finished": len(self.finished),
            "total_active": len(self.waiting) + len(self.running)
        }
