"""
Core engine for Mini-vLLM
Coordinates scheduler and model runner to process requests
"""
from typing import List, Optional, Dict, Any
import logging
import time

from .data_structures import (
    Request, SequenceGroup, RequestOutput, SequenceStatus, SamplingParams
)
from .scheduler import MiniScheduler
from .model_runner import MiniModelRunner

logger = logging.getLogger(__name__)


class MiniEngine:
    """
    Core engine that coordinates request processing
    
    This is the heart of Mini-vLLM - it:
    1. Manages the request lifecycle
    2. Coordinates scheduler and model runner
    3. Implements the main step() loop
    """
    
    def __init__(
        self,
        model_name: str,
        max_batch_size: int = 4,
        max_seq_len: int = 512,
        device: str = "auto"
    ):
        self.model_name = model_name
        
        logger.info(f"Initializing MiniEngine with model {model_name}")
        
        # Initialize components
        self.model_runner = MiniModelRunner(model_name, device)
        self.scheduler = MiniScheduler(max_batch_size, max_seq_len)
        
        # Get tokenizer from model runner
        self.tokenizer = self.model_runner.get_tokenizer()
        
        # Request tracking
        self.request_counter = 0
        self.pending_requests: Dict[str, SequenceGroup] = {}
        
        logger.info("MiniEngine initialized successfully")
    
    def add_request(
        self,
        prompt: str,
        sampling_params: Optional[SamplingParams] = None,
        request_id: Optional[str] = None
    ) -> str:
        """
        Add a new generation request
        
        Args:
            prompt: Input text prompt
            sampling_params: Generation parameters
            request_id: Optional custom request ID
            
        Returns:
            Request ID
        """
        if sampling_params is None:
            sampling_params = SamplingParams()
        
        if request_id is None:
            self.request_counter += 1
            request_id = f"req_{self.request_counter}"
        
        # Create request and sequence group
        request = Request(
            request_id=request_id,
            prompt=prompt,
            sampling_params=sampling_params
        )
        
        sequence_group = SequenceGroup.from_request(request, self.tokenizer)
        
        # Add to scheduler and tracking
        self.scheduler.add_sequence_group(sequence_group)
        self.pending_requests[request_id] = sequence_group
        
        logger.debug(f"Added request {request_id}: '{prompt[:50]}...'")
        return request_id
    
    def step(self) -> List[RequestOutput]:
        """
        Execute one step of the generation process
        
        This is the core method that:
        1. Asks scheduler what to process
        2. Runs model inference
        3. Updates sequences with new tokens
        4. Returns completed requests
        
        Returns:
            List of completed RequestOutput objects
        """
        # Step 1: Get scheduling decisions
        scheduler_output = self.scheduler.schedule()
        
        if scheduler_output.is_empty:
            # Nothing to process
            return []
        
        logger.debug(f"Processing {len(scheduler_output.scheduled_groups)} sequence groups")
        
        # Step 2: Prepare model inputs
        model_input = self.model_runner.prepare_inputs(scheduler_output.scheduled_groups)
        
        # Step 3: Execute model
        model_output = self.model_runner.execute_model(model_input)
        
        # Step 4: Process model outputs and update sequences
        self._process_model_outputs(scheduler_output.scheduled_groups, model_output)

        # Step 4.5: Update scheduler with finished sequences
        self.scheduler._update_finished()

        # Step 5: Collect finished requests
        finished_outputs = self._collect_finished_requests()
        
        return finished_outputs
    
    def _process_model_outputs(
        self,
        sequence_groups: List[SequenceGroup],
        model_output
    ) -> None:
        """
        Process model outputs and update sequences
        """
        for i, (group, next_token_id) in enumerate(zip(sequence_groups, model_output.next_token_ids)):
            seq = group.get_main_sequence()
            
            # Add the new token
            seq.add_token(next_token_id)
            
            # Check if we should stop generation
            if self.model_runner.check_stop_condition(group, next_token_id):
                seq.status = SequenceStatus.FINISHED
                logger.debug(f"Sequence {seq.seq_id} finished generation")
    
    def _collect_finished_requests(self) -> List[RequestOutput]:
        """
        Collect and return finished requests
        """
        finished_groups = self.scheduler.get_finished_requests()
        outputs = []
        
        for group in finished_groups:
            seq = group.get_main_sequence()
            
            # Generate output text
            output_text = seq.get_output_text(self.tokenizer)
            
            # Determine finish reason
            finish_reason = "stop"  # Simplified - could be "length", "stop_sequence", etc.
            
            # Create request output
            request_output = RequestOutput(
                request_id=group.request_id,
                prompt=seq.prompt,
                text=output_text,
                finished=True,
                finish_reason=finish_reason
            )
            
            outputs.append(request_output)
            
            # Remove from pending requests
            if group.request_id in self.pending_requests:
                del self.pending_requests[group.request_id]
            
            logger.debug(f"Request {group.request_id} completed: '{output_text[:50]}...'")
        
        return outputs
    
    def has_unfinished_requests(self) -> bool:
        """Check if there are any unfinished requests"""
        return self.scheduler.has_unfinished_requests()
    
    def abort_request(self, request_id: str) -> bool:
        """
        Abort a request
        
        Args:
            request_id: ID of request to abort
            
        Returns:
            True if request was found and aborted
        """
        success = self.scheduler.abort_request(request_id)
        if success and request_id in self.pending_requests:
            del self.pending_requests[request_id]
        return success
    
    def get_stats(self) -> Dict[str, Any]:
        """Get engine statistics"""
        scheduler_stats = self.scheduler.get_stats()
        
        return {
            "model_name": self.model_name,
            "pending_requests": len(self.pending_requests),
            "scheduler": scheduler_stats,
            "total_requests_processed": self.request_counter
        }
    
    def generate(
        self,
        prompts: List[str],
        sampling_params: Optional[SamplingParams] = None
    ) -> List[RequestOutput]:
        """
        Convenience method for batch generation (like vLLM's LLM.generate)
        
        Args:
            prompts: List of input prompts
            sampling_params: Generation parameters
            
        Returns:
            List of RequestOutput objects
        """
        if sampling_params is None:
            sampling_params = SamplingParams()
        
        # Add all requests
        request_ids = []
        for prompt in prompts:
            request_id = self.add_request(prompt, sampling_params)
            request_ids.append(request_id)
        
        # Run until all requests are finished
        all_outputs = []
        step_count = 0
        while self.has_unfinished_requests():
            step_count += 1
            step_outputs = self.step()
            all_outputs.extend(step_outputs)
            logger.debug(f"Step {step_count}: got {len(step_outputs)} outputs, total: {len(all_outputs)}")

            # Safety check
            if step_count > 100:
                logger.warning("Too many steps, breaking to avoid infinite loop")
                break

        # Sort outputs by request order
        output_map = {output.request_id: output for output in all_outputs}
        sorted_outputs = [output_map[req_id] for req_id in request_ids if req_id in output_map]

        logger.debug(f"Generated {len(sorted_outputs)} outputs for {len(request_ids)} requests")
        return sorted_outputs
