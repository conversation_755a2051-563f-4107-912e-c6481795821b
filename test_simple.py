#!/usr/bin/env python3
"""
Simple test to debug Mini-vLLM
"""

import logging
from mini_vllm import MiniLL<PERSON>, SamplingParams

# Set up detailed logging
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s - %(name)s - %(message)s')
logger = logging.getLogger(__name__)

def test_single_request():
    print("Testing single request...")
    
    # Initialize with very simple settings
    llm = MiniLLM("gpt2", max_batch_size=1, max_seq_len=20)
    
    # Simple prompt
    prompt = "Hello"
    sampling_params = SamplingParams(temperature=0.0, max_tokens=5)  # Greedy, short
    
    print(f"Generating for prompt: '{prompt}'")
    outputs = llm.generate([prompt], sampling_params)
    
    print(f"Got {len(outputs)} outputs")
    for output in outputs:
        print(f"  Request: {output.request_id}")
        print(f"  Prompt: {output.prompt}")
        print(f"  Generated: '{output.text}'")
        print(f"  Finished: {output.finished}")

if __name__ == "__main__":
    try:
        test_single_request()
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
