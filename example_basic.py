#!/usr/bin/env python3
"""
Basic example demonstrating Mini-vLLM usage

This example shows how to use Mini-vLLM for text generation,
following the same patterns as the original vLLM.
"""

import logging
from mini_vllm import MiniLLM, SamplingParams, generate_text

# Set up logging to see what's happening
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)


def main():
    print("🚀 Mini-vLLM Basic Example")
    print("=" * 50)
    
    # Example 1: Simple text generation
    print("\n📝 Example 1: Basic Text Generation")
    print("-" * 30)
    
    # Initialize the LLM (using a small model for demo)
    print("Loading model...")
    llm = MiniLLM(
        model="gpt2",  # Small model for quick demo
        max_batch_size=2,
        max_seq_len=100
    )
    
    # Define prompts
    prompts = [
        "The future of artificial intelligence is",
        "Once upon a time in a distant galaxy",
        "The most important thing in life is"
    ]
    
    # Set generation parameters
    sampling_params = SamplingParams(
        temperature=0.8,
        max_tokens=50,
        top_p=0.9,
        stop=[".", "!", "?"]  # Stop at sentence endings
    )
    
    print(f"Generating completions for {len(prompts)} prompts...")
    
    # Generate completions
    outputs = llm.generate(prompts, sampling_params)
    
    # Display results
    for i, output in enumerate(outputs):
        print(f"\nPrompt {i+1}: {output.prompt}")
        print(f"Generated: {output.text}")
        print(f"Finished: {output.finished}")
        print(f"Finish reason: {output.finish_reason}")
    
    # Example 2: Different sampling parameters
    print("\n🎲 Example 2: Different Sampling Parameters")
    print("-" * 30)
    
    prompt = "The key to happiness is"
    
    # Greedy sampling (temperature=0)
    greedy_params = SamplingParams(temperature=0.0, max_tokens=30)
    greedy_output = llm.generate([prompt], greedy_params)[0]
    
    # Creative sampling (high temperature)
    creative_params = SamplingParams(temperature=1.5, max_tokens=30)
    creative_output = llm.generate([prompt], creative_params)[0]
    
    print(f"Prompt: {prompt}")
    print(f"Greedy (temp=0.0): {greedy_output.text}")
    print(f"Creative (temp=1.5): {creative_output.text}")
    
    # Example 3: Chat interface
    print("\n💬 Example 3: Simple Chat Interface")
    print("-" * 30)
    
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "What is the capital of France?"}
    ]
    
    chat_params = SamplingParams(temperature=0.7, max_tokens=50)
    chat_output = llm.chat(messages, chat_params)
    
    print("Chat conversation:")
    for msg in messages:
        print(f"{msg['role'].title()}: {msg['content']}")
    print(f"Assistant: {chat_output.text}")
    
    # Example 4: Convenience function
    print("\n⚡ Example 4: Convenience Function")
    print("-" * 30)
    
    quick_prompts = [
        "Python is a programming language that",
        "Machine learning is"
    ]
    
    quick_texts = generate_text(
        model="gpt2",
        prompts=quick_prompts,
        temperature=0.8,
        max_tokens=25,
        max_batch_size=2
    )
    
    for prompt, text in zip(quick_prompts, quick_texts):
        print(f"Prompt: {prompt}")
        print(f"Generated: {text}")
        print()
    
    # Example 5: Engine statistics
    print("\n📊 Example 5: Engine Statistics")
    print("-" * 30)
    
    stats = llm.get_stats()
    print("Engine Statistics:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    print("\n✅ All examples completed successfully!")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️  Interrupted by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
