# Mini-vLLM: A Simplified LLM Inference Engine

A minimal, educational implementation of vLLM's core architecture. This project demonstrates the key concepts behind high-performance LLM serving in a simplified, easy-to-understand codebase.

## 🎯 Purpose

Mini-vLLM is designed to help you understand:
- How LLM inference engines work internally
- The architecture patterns used by vLLM
- Request scheduling and batching strategies
- The flow from user request to generated response

## 🏗️ Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Mini-vLLM Architecture                   │
│                                                             │
│  MiniLLM (User Interface)                                  │
│      ↓ generate(prompts)                                   │
│  MiniEngine (Coordination)                                 │
│      ↓ add_request() + step()                             │
│  MiniScheduler ←→ MiniModelRunner                          │
│      ↓                    ↓                                │
│  Request Queue        HuggingFace Model                    │
└─────────────────────────────────────────────────────────────┘
```

### Core Components

- **MiniLLM**: User-facing interface (like vLLM's `LLM` class)
- **MiniEngine**: Coordinates request processing and manages the generation loop
- **MiniScheduler**: Manages request queues and batching decisions
- **MiniModelRunner**: Handles model loading and inference using HuggingFace transformers
- **Data Structures**: Request, Sequence, SamplingParams, etc.

## 🚀 Quick Start

### Installation

```bash
# Install dependencies
pip install -r requirements.txt
```

### Basic Usage

```python
from mini_vllm import MiniLLM, SamplingParams

# Initialize the LLM
llm = MiniLLM("gpt2")

# Generate text
prompts = ["The future of AI is", "Once upon a time"]
sampling_params = SamplingParams(temperature=0.8, max_tokens=50)

outputs = llm.generate(prompts, sampling_params)

for output in outputs:
    print(f"Prompt: {output.prompt}")
    print(f"Generated: {output.text}")
```

### Run Examples

```bash
# Basic usage example
python example_basic.py

# Step-by-step internal workflow
python example_step_by_step.py
```

## 🔍 Key Simplifications

Compared to full vLLM, Mini-vLLM makes these simplifications for clarity:

| Feature | vLLM | Mini-vLLM |
|---------|------|-----------|
| **Distributed Execution** | Multi-GPU, multi-node | Single GPU/CPU only |
| **Memory Management** | PagedAttention, KV cache blocks | Simple padding |
| **Model Backend** | Custom CUDA kernels | HuggingFace transformers |
| **Scheduling** | Complex policies, preemption | Simple FIFO queue |
| **Batching** | Continuous batching | Basic static batching |
| **Streaming** | Real-time streaming | Batch responses only |
| **Quantization** | Multiple formats | None |

## 📚 Learning Path

1. **Start with `example_basic.py`** - See the user interface
2. **Run `example_step_by_step.py`** - Understand the internal flow
3. **Read the code in order**:
   - `data_structures.py` - Core data types
   - `scheduler.py` - Request management
   - `model_runner.py` - Model inference
   - `engine.py` - Coordination logic
   - `llm.py` - User interface

## 🔄 Request Flow

Here's how a request flows through Mini-vLLM:

1. **User calls** `llm.generate(prompts)`
2. **Engine adds** requests to scheduler queue
3. **Scheduler decides** which requests to process (batching)
4. **ModelRunner prepares** input tensors
5. **Model executes** forward pass and samples tokens
6. **Engine updates** sequences with new tokens
7. **Repeat steps 3-6** until generation complete
8. **Return** completed outputs to user

## 🎛️ Configuration

```python
llm = MiniLLM(
    model="gpt2",           # HuggingFace model name
    max_batch_size=4,       # Maximum requests per batch
    max_seq_len=512,        # Maximum sequence length
    device="auto"           # "auto", "cuda", or "cpu"
)

sampling_params = SamplingParams(
    temperature=0.8,        # Sampling temperature
    top_p=0.9,             # Nucleus sampling
    top_k=50,              # Top-k sampling
    max_tokens=100,        # Maximum tokens to generate
    stop=[".", "!", "?"]   # Stop sequences
)
```

## 🧪 Experiments

Try these experiments to understand the concepts:

### Batching Behavior
```python
# See how requests are batched together
engine = MiniEngine("gpt2", max_batch_size=2)

# Add multiple requests
for i in range(5):
    engine.add_request(f"Prompt {i}", sampling_params)

# Watch batching in action
while engine.has_unfinished_requests():
    outputs = engine.step()
    print(f"Processed batch, completed: {len(outputs)}")
```

### Scheduling Policies
```python
# Observe FIFO scheduling
scheduler = MiniScheduler(max_batch_size=3)

# Add requests and see order
for prompt in ["First", "Second", "Third", "Fourth"]:
    seq_group = SequenceGroup.from_request(
        Request.create(prompt, sampling_params), 
        tokenizer
    )
    scheduler.add_sequence_group(seq_group)

# See which get scheduled first
output = scheduler.schedule()
print([g.request_id for g in output.scheduled_groups])
```

## 🔧 Extending Mini-vLLM

Want to add features? Here are some ideas:

- **Streaming support**: Modify the engine to yield partial results
- **Priority scheduling**: Add priority queues to the scheduler
- **Better batching**: Implement continuous batching
- **Memory optimization**: Add simple KV cache management
- **More sampling methods**: Add beam search, repetition penalties

## 📖 Comparison with vLLM

| Concept | Mini-vLLM Implementation | vLLM Implementation |
|---------|-------------------------|-------------------|
| **Request Management** | Simple FIFO queue | Complex priority scheduling |
| **Batching** | Static batching | Continuous batching |
| **Memory** | Padding to max length | PagedAttention blocks |
| **Sampling** | Basic temperature/top-p | Advanced sampling algorithms |
| **Model Loading** | HuggingFace AutoModel | Custom model implementations |

## 🤝 Contributing

This is an educational project! Feel free to:
- Add more examples
- Improve documentation
- Implement additional features
- Fix bugs or optimize performance

## 📄 License

MIT License - feel free to use this for learning and teaching!

---

**Note**: This is a simplified educational implementation. For production use, please use the full [vLLM](https://github.com/vllm-project/vllm) library.
