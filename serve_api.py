#!/usr/bin/env python3
"""
Mini-vLLM API Server
OpenAI-compatible API server for Mini-vLLM with streaming support
"""
import argparse
import asyncio
import logging
import sys
from typing import Dict, Any

import uvicorn

from mini_vllm.api_server import create_app


def setup_logging(log_level: str = "INFO"):
    """Setup logging configuration"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )


def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Mini-vLLM API Server",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    # Server configuration
    parser.add_argument("--host", type=str, default="0.0.0.0",
                       help="Host to bind the server to")
    parser.add_argument("--port", type=int, default=8000,
                       help="Port to bind the server to")
    parser.add_argument("--log-level", type=str, default="INFO",
                       choices=["DEBUG", "INFO", "WARNING", "ERROR"],
                       help="Logging level")
    
    # Model configuration
    parser.add_argument("--model", type=str, default="gpt2",
                       help="HuggingFace model name or path")
    parser.add_argument("--device", type=str, default="auto",
                       choices=["auto", "cpu", "cuda"],
                       help="Device to run the model on")
    
    # Engine configuration
    parser.add_argument("--max-batch-size", type=int, default=4,
                       help="Maximum batch size for inference")
    parser.add_argument("--max-seq-len", type=int, default=512,
                       help="Maximum sequence length")
    
    # API configuration
    parser.add_argument("--disable-log-stats", action="store_true",
                       help="Disable periodic logging of stats")
    parser.add_argument("--api-key", type=str, default=None,
                       help="API key for authentication (optional)")
    
    return parser.parse_args()


def create_engine_args(args) -> Dict[str, Any]:
    """Create engine arguments from command line args"""
    return {
        "model_name": args.model,
        "max_batch_size": args.max_batch_size,
        "max_seq_len": args.max_seq_len,
        "device": args.device
    }


async def run_server(args):
    """Run the API server"""
    # Setup logging
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)
    
    logger.info("Starting Mini-vLLM API Server")
    logger.info(f"Model: {args.model}")
    logger.info(f"Device: {args.device}")
    logger.info(f"Max batch size: {args.max_batch_size}")
    logger.info(f"Max sequence length: {args.max_seq_len}")
    logger.info(f"Server: http://{args.host}:{args.port}")
    
    # Create engine arguments
    engine_args = create_engine_args(args)
    
    # Create FastAPI app
    app = create_app(engine_args)
    
    # Configure uvicorn
    config = uvicorn.Config(
        app=app,
        host=args.host,
        port=args.port,
        log_level=args.log_level.lower(),
        access_log=True,
        loop="asyncio"
    )
    
    # Create and run server
    server = uvicorn.Server(config)
    
    try:
        await server.serve()
    except KeyboardInterrupt:
        logger.info("Received interrupt signal, shutting down...")
    except Exception as e:
        logger.error(f"Server error: {e}")
        raise
    finally:
        logger.info("Server shutdown complete")


def main():
    """Main entry point"""
    args = parse_args()
    
    try:
        asyncio.run(run_server(args))
    except KeyboardInterrupt:
        print("\nShutdown requested by user")
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
