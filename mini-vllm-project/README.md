# Mini-vLLM

A simplified, educational implementation of vLLM's core architecture with OpenAI-compatible API.

[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)

## 🎯 Purpose

Mini-vLLM is designed to help you understand:
- How LLM inference engines work internally
- The architecture patterns used by vLLM
- Request scheduling and batching strategies
- OpenAI-compatible API implementation
- The flow from user request to generated response

## 🏗️ Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Mini-vLLM Architecture                   │
│                                                             │
│  FastAPI Server (OpenAI-compatible)                        │
│      ↓ HTTP/SSE                                            │
│  AsyncLLMEngine (Async Wrapper)                            │
│      ↓ Background Processing                               │
│  MiniEngine (Core Coordination)                            │
│      ↓ Request Management                                  │
│  MiniScheduler ←→ MiniModelRunner                          │
│      ↓                    ↓                                │
│  Request Queue        HuggingFace Model                    │
└─────────────────────────────────────────────────────────────┘
```

### Core Components

- **MiniLLM**: User-facing interface (like vLLM's `LLM` class)
- **MiniEngine**: Coordinates request processing and manages the generation loop
- **MiniScheduler**: Manages request queues and batching decisions
- **MiniModelRunner**: Handles model loading and inference using HuggingFace transformers
- **AsyncLLMEngine**: Async wrapper for non-blocking API serving
- **FastAPI Server**: OpenAI-compatible API with streaming support

## 🚀 Quick Start

### Installation

```bash
# Clone the repository
git clone https://github.com/mini-vllm/mini-vllm.git
cd mini-vllm

# Install with Poetry
poetry install

# Or install with pip
pip install -e .
```

### Basic Usage

```python
from mini_vllm import MiniLLM, SamplingParams

# Initialize the LLM
llm = MiniLLM("gpt2")

# Generate text
prompts = ["The future of AI is", "Once upon a time"]
sampling_params = SamplingParams(temperature=0.8, max_tokens=50)

outputs = llm.generate(prompts, sampling_params)

for output in outputs:
    print(f"Prompt: {output.prompt}")
    print(f"Generated: {output.text}")
```

### API Server

```bash
# Start the server
mini-vllm-serve --model gpt2 --host 0.0.0.0 --port 8000

# Or with Poetry
poetry run mini-vllm-serve --model gpt2
```

### Client Usage

```bash
# Interactive chat
mini-vllm-client chat --stream

# Single message
mini-vllm-client message "Hello, how are you?"

# Health check
mini-vllm-client health
```

## 📚 API Compatibility

Mini-vLLM implements OpenAI-compatible endpoints:

- `POST /v1/chat/completions` - Chat completions with streaming
- `POST /v1/completions` - Text completions
- `GET /v1/models` - List available models
- `GET /health` - Health check

### Example with OpenAI Python Client

```python
from openai import OpenAI

client = OpenAI(
    api_key="dummy-key",
    base_url="http://localhost:8000/v1"
)

response = client.chat.completions.create(
    model="gpt2",
    messages=[{"role": "user", "content": "Hello!"}],
    stream=True
)

for chunk in response:
    print(chunk.choices[0].delta.content, end="")
```

## 🔍 Key Simplifications

Compared to full vLLM, Mini-vLLM makes these simplifications for clarity:

| Feature | vLLM | Mini-vLLM |
|---------|------|-----------|
| **Distributed Execution** | Multi-GPU, multi-node | Single GPU/CPU only |
| **Memory Management** | PagedAttention, KV cache blocks | Simple padding |
| **Model Backend** | Custom CUDA kernels | HuggingFace transformers |
| **Scheduling** | Complex policies, preemption | Simple FIFO queue |
| **Batching** | Continuous batching | Basic static batching |
| **Streaming** | Real-time streaming | Server-Sent Events |

## 🧪 Examples

Check out the `examples/` directory for:
- Basic inference examples
- API client examples
- Streaming demonstrations
- Performance comparisons

## 🛠️ Development

### Setup Development Environment

```bash
# Install with dev dependencies
poetry install --with dev

# Install pre-commit hooks
poetry run pre-commit install

# Run tests
poetry run pytest

# Format code
poetry run black src/ tests/
poetry run isort src/ tests/

# Type checking
poetry run mypy src/
```

### Project Structure

```
mini-vllm/
├── src/mini_vllm/           # Main package
│   ├── core/                # Core inference engine
│   ├── api/                 # OpenAI-compatible API
│   └── cli/                 # Command-line tools
├── tests/                   # Test suite
├── examples/                # Usage examples
├── docs/                    # Documentation
└── pyproject.toml          # Project configuration
```

## 📖 Learning Path

1. **Start with basic inference**: `examples/basic_inference.py`
2. **Understand the architecture**: Read the core module documentation
3. **Try the API**: Start the server and use the client
4. **Explore internals**: Look at the step-by-step examples
5. **Compare with vLLM**: See the architectural differences

## 🤝 Contributing

Contributions are welcome! This is an educational project, so:

- Focus on clarity over performance
- Add comprehensive documentation
- Include examples for new features
- Maintain the educational value

## 📄 License

MIT License - feel free to use this for learning and teaching!

## 🙏 Acknowledgments

- [vLLM](https://github.com/vllm-project/vllm) - The original high-performance LLM serving system
- [HuggingFace Transformers](https://github.com/huggingface/transformers) - For the model implementations
- [FastAPI](https://fastapi.tiangolo.com/) - For the API framework

---

**Note**: This is a simplified educational implementation. For production use, please use the full [vLLM](https://github.com/vllm-project/vllm) library.
