#!/usr/bin/env python3
"""
Mini-vLLM Client CLI
Simple command-line client for testing Mini-vLLM API
"""
import argparse
import asyncio
import json
import sys
from typing import List

import httpx


async def chat_completion(
    base_url: str,
    model: str,
    messages: List[dict],
    temperature: float = 0.7,
    max_tokens: int = 100,
    stream: bool = False
):
    """Send a chat completion request"""
    url = f"{base_url}/v1/chat/completions"
    
    payload = {
        "model": model,
        "messages": messages,
        "temperature": temperature,
        "max_tokens": max_tokens,
        "stream": stream
    }
    
    async with httpx.AsyncClient(timeout=60.0) as client:
        if stream:
            print("Assistant: ", end="", flush=True)
            async with client.stream("POST", url, json=payload) as response:
                response.raise_for_status()
                
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data = line[6:]
                        
                        if data == "[DONE]":
                            break
                        
                        try:
                            chunk = json.loads(data)
                            delta = chunk["choices"][0]["delta"]
                            
                            if "content" in delta and delta["content"]:
                                print(delta["content"], end="", flush=True)
                        except json.JSONDecodeError:
                            continue
            print()  # New line after streaming
        else:
            response = await client.post(url, json=payload)
            response.raise_for_status()
            result = response.json()
            
            assistant_message = result["choices"][0]["message"]["content"]
            print(f"Assistant: {assistant_message}")
            
            if "usage" in result:
                usage = result["usage"]
                print(f"Usage: {usage['total_tokens']} tokens")


async def health_check(base_url: str):
    """Check server health"""
    url = f"{base_url}/health"
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        try:
            response = await client.get(url)
            response.raise_for_status()
            health = response.json()
            
            print(f"✅ Server is healthy")
            print(f"   Status: {health['status']}")
            print(f"   Model: {health.get('model', 'unknown')}")
            print(f"   Version: {health.get('version', 'unknown')}")
            
        except httpx.ConnectError:
            print(f"❌ Cannot connect to server at {base_url}")
            return False
        except Exception as e:
            print(f"❌ Health check failed: {e}")
            return False
    
    return True


def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Mini-vLLM Client CLI",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    parser.add_argument("--base-url", type=str, default="http://localhost:8000",
                       help="Base URL of the Mini-vLLM server")
    parser.add_argument("--model", type=str, default="gpt2",
                       help="Model name to use")
    
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Health check command
    subparsers.add_parser("health", help="Check server health")
    
    # Chat command
    chat_parser = subparsers.add_parser("chat", help="Start interactive chat")
    chat_parser.add_argument("--temperature", type=float, default=0.7,
                           help="Sampling temperature")
    chat_parser.add_argument("--max-tokens", type=int, default=100,
                           help="Maximum tokens to generate")
    chat_parser.add_argument("--stream", action="store_true",
                           help="Enable streaming responses")
    
    # Single message command
    msg_parser = subparsers.add_parser("message", help="Send a single message")
    msg_parser.add_argument("text", help="Message text to send")
    msg_parser.add_argument("--temperature", type=float, default=0.7,
                          help="Sampling temperature")
    msg_parser.add_argument("--max-tokens", type=int, default=100,
                          help="Maximum tokens to generate")
    msg_parser.add_argument("--stream", action="store_true",
                          help="Enable streaming responses")
    msg_parser.add_argument("--system", type=str,
                          help="System message to include")
    
    return parser.parse_args()


async def interactive_chat(args):
    """Run interactive chat session"""
    print(f"🤖 Mini-vLLM Interactive Chat")
    print(f"Connected to: {args.base_url}")
    print(f"Model: {args.model}")
    print("Type 'quit' or 'exit' to end the session")
    print("-" * 50)
    
    messages = []
    
    while True:
        try:
            user_input = input("\nYou: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("Goodbye!")
                break
            
            if not user_input:
                continue
            
            # Add user message
            messages.append({"role": "user", "content": user_input})
            
            # Get response
            await chat_completion(
                args.base_url,
                args.model,
                messages,
                args.temperature,
                args.max_tokens,
                args.stream
            )
            
            # Note: In a real implementation, we'd capture the assistant's response
            # and add it to the messages list for context
            
        except KeyboardInterrupt:
            print("\n\nGoodbye!")
            break
        except Exception as e:
            print(f"Error: {e}")


async def main():
    """Main entry point"""
    args = parse_args()
    
    if not args.command:
        print("Please specify a command. Use --help for more information.")
        return
    
    try:
        if args.command == "health":
            await health_check(args.base_url)
        
        elif args.command == "chat":
            # First check if server is healthy
            if await health_check(args.base_url):
                await interactive_chat(args)
        
        elif args.command == "message":
            # First check if server is healthy
            if not await health_check(args.base_url):
                return
            
            messages = []
            if args.system:
                messages.append({"role": "system", "content": args.system})
            messages.append({"role": "user", "content": args.text})
            
            print(f"User: {args.text}")
            await chat_completion(
                args.base_url,
                args.model,
                messages,
                args.temperature,
                args.max_tokens,
                args.stream
            )
    
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


def main_sync():
    """Synchronous entry point for Poetry script"""
    asyncio.run(main())


if __name__ == "__main__":
    main_sync()
