"""
OpenAI-compatible API server for Mini-vLLM

This module provides a FastAPI-based server that implements OpenAI's API format:
- Chat completions endpoint (/v1/chat/completions)
- Text completions endpoint (/v1/completions)  
- Model listing endpoint (/v1/models)
- Health check endpoint (/health)
- Server-Sent Events streaming support
- Async request processing

Example usage:
    >>> from mini_vllm.api import create_app
    >>> app = create_app({"model_name": "gpt2"})
    >>> # Run with: uvicorn app:app --host 0.0.0.0 --port 8000
"""

from .server import create_app, MiniVLLMServer
from .protocol import (
    ChatCompletionRequest,
    ChatCompletionResponse,
    CompletionRequest,
    CompletionResponse,
    ModelListResponse,
    HealthResponse,
)
from .async_engine import AsyncLLMEngine

__all__ = [
    # Main server components
    "create_app",
    "MiniVLLMServer",
    "AsyncLLMEngine",
    
    # Protocol models
    "ChatCompletionRequest",
    "ChatCompletionResponse", 
    "CompletionRequest",
    "CompletionResponse",
    "ModelListResponse",
    "HealthResponse",
]
