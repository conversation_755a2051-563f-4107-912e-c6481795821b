"""
Async wrapper for Mini-vLLM engine to support non-blocking API serving
"""
import asyncio
import threading
import uuid
from typing import AsyncGenerator, Dict, List, Optional, Any
import logging
from queue import Queue, Empty
import time

from ..core.engine import MiniEngine
from ..core.data_structures import SamplingParams, RequestOutput

logger = logging.getLogger(__name__)


class AsyncRequestTracker:
    """Tracks async requests and their completion status"""
    
    def __init__(self):
        self.pending_requests: Dict[str, asyncio.Future] = {}
        self.request_outputs: Dict[str, RequestOutput] = {}
        self.streaming_requests: Dict[str, Queue] = {}
    
    def add_request(self, request_id: str) -> asyncio.Future:
        """Add a new request and return a future for its completion"""
        future = asyncio.Future()
        self.pending_requests[request_id] = future
        return future
    
    def add_streaming_request(self, request_id: str) -> Queue:
        """Add a streaming request and return a queue for partial results"""
        queue = Queue()
        self.streaming_requests[request_id] = queue
        return queue
    
    def complete_request(self, request_id: str, output: RequestOutput):
        """Mark a request as completed"""
        if request_id in self.pending_requests:
            future = self.pending_requests.pop(request_id)
            if not future.cancelled():
                future.set_result(output)
        
        # Also store for potential retrieval
        self.request_outputs[request_id] = output
    
    def stream_partial(self, request_id: str, partial_output: RequestOutput):
        """Send partial output to streaming request"""
        if request_id in self.streaming_requests:
            queue = self.streaming_requests[request_id]
            queue.put(partial_output)
    
    def finish_streaming(self, request_id: str):
        """Mark streaming request as finished"""
        if request_id in self.streaming_requests:
            queue = self.streaming_requests[request_id]
            queue.put(None)  # Sentinel value for completion
            del self.streaming_requests[request_id]
    
    def cancel_request(self, request_id: str):
        """Cancel a pending request"""
        if request_id in self.pending_requests:
            future = self.pending_requests.pop(request_id)
            if not future.cancelled():
                future.cancel()
        
        if request_id in self.streaming_requests:
            del self.streaming_requests[request_id]


class AsyncLLMEngine:
    """
    Async wrapper around MiniEngine for non-blocking API serving
    
    This class runs the MiniEngine in a background thread and provides
    async interfaces for adding requests and getting results.
    """
    
    def __init__(self, engine_args: Dict[str, Any]):
        """Initialize the async engine"""
        self.engine_args = engine_args
        self.engine: Optional[MiniEngine] = None
        self.background_thread: Optional[threading.Thread] = None
        self.request_tracker = AsyncRequestTracker()
        self.running = False
        self.request_queue: Queue = Queue()
        
        logger.info("AsyncLLMEngine initialized")
    
    async def start_background_loop(self):
        """Start the background engine loop"""
        if self.running:
            return
        
        self.running = True
        
        # Initialize engine in background thread
        def init_and_run():
            try:
                # Create engine
                self.engine = MiniEngine(**self.engine_args)
                logger.info("Background engine initialized")
                
                # Main processing loop
                self._background_loop()
            except Exception as e:
                logger.error(f"Background engine failed: {e}")
                self.running = False
        
        self.background_thread = threading.Thread(target=init_and_run, daemon=True)
        self.background_thread.start()
        
        # Wait for engine to be ready
        while self.engine is None and self.running:
            await asyncio.sleep(0.01)
        
        logger.info("AsyncLLMEngine background loop started")
    
    def _background_loop(self):
        """Main background processing loop"""
        logger.info("Starting background processing loop")
        
        while self.running:
            try:
                # Process new requests from queue
                self._process_new_requests()
                
                # Run engine step if there are active requests
                if self.engine.has_unfinished_requests():
                    step_outputs = self.engine.step()
                    
                    # Handle completed requests
                    for output in step_outputs:
                        self._handle_completed_request(output)
                else:
                    # No active requests, sleep briefly
                    time.sleep(0.001)
                    
            except Exception as e:
                logger.error(f"Error in background loop: {e}")
                time.sleep(0.01)
        
        logger.info("Background processing loop stopped")
    
    def _process_new_requests(self):
        """Process new requests from the queue"""
        while True:
            try:
                request_data = self.request_queue.get_nowait()
                request_id = request_data["request_id"]
                prompt = request_data["prompt"]
                sampling_params = request_data["sampling_params"]
                
                # Add to engine
                self.engine.add_request(prompt, sampling_params, request_id)
                logger.debug(f"Added request {request_id} to engine")
                
            except Empty:
                break
            except Exception as e:
                logger.error(f"Error processing new request: {e}")
    
    def _handle_completed_request(self, output: RequestOutput):
        """Handle a completed request output"""
        request_id = output.request_id

        # Complete non-streaming requests
        self.request_tracker.complete_request(request_id, output)

        # Handle streaming requests (send final chunk)
        if request_id in self.request_tracker.streaming_requests:
            self.request_tracker.stream_partial(request_id, output)
            self.request_tracker.finish_streaming(request_id)

        logger.debug(f"Completed request {request_id}")

    def _handle_partial_request(self, request_id: str, partial_text: str, is_finished: bool = False):
        """Handle partial output for streaming requests"""
        if request_id in self.request_tracker.streaming_requests:
            # Create partial output
            partial_output = RequestOutput(
                request_id=request_id,
                prompt="",  # We don't need the full prompt for streaming
                text=partial_text,
                finished=is_finished,
                finish_reason="stop" if is_finished else None
            )

            self.request_tracker.stream_partial(request_id, partial_output)

            if is_finished:
                self.request_tracker.finish_streaming(request_id)
    
    async def generate(
        self,
        prompt: str,
        sampling_params: SamplingParams,
        request_id: Optional[str] = None
    ) -> RequestOutput:
        """Generate completion for a single prompt (non-streaming)"""
        if not self.running:
            await self.start_background_loop()
        
        if request_id is None:
            request_id = f"req_{uuid.uuid4().hex[:8]}"
        
        # Add request to tracker
        future = self.request_tracker.add_request(request_id)
        
        # Queue request for background processing
        self.request_queue.put({
            "request_id": request_id,
            "prompt": prompt,
            "sampling_params": sampling_params
        })
        
        # Wait for completion
        try:
            output = await future
            return output
        except asyncio.CancelledError:
            # Clean up cancelled request
            self.request_tracker.cancel_request(request_id)
            if self.engine:
                self.engine.abort_request(request_id)
            raise
    
    async def generate_stream(
        self,
        prompt: str,
        sampling_params: SamplingParams,
        request_id: Optional[str] = None
    ) -> AsyncGenerator[str, None]:
        """Generate completion with streaming (yields individual tokens)"""
        if not self.running:
            await self.start_background_loop()

        if request_id is None:
            request_id = f"req_{uuid.uuid4().hex[:8]}"

        # Add streaming request to tracker
        queue = self.request_tracker.add_streaming_request(request_id)

        # Queue request for background processing with streaming flag
        self.request_queue.put({
            "request_id": request_id,
            "prompt": prompt,
            "sampling_params": sampling_params,
            "streaming": True
        })

        # Yield tokens as they become available
        try:
            current_text = ""
            while True:
                # Wait for next partial result
                while True:
                    try:
                        partial_output = queue.get(timeout=0.01)
                        break
                    except Empty:
                        await asyncio.sleep(0.001)

                if partial_output is None:
                    # Sentinel value indicates completion
                    break

                # Extract new token(s) from the partial output
                new_text = partial_output.text
                if len(new_text) > len(current_text):
                    new_token = new_text[len(current_text):]
                    current_text = new_text
                    yield new_token

                # If finished, break
                if partial_output.finished:
                    break

        except asyncio.CancelledError:
            # Clean up cancelled request
            self.request_tracker.cancel_request(request_id)
            if self.engine:
                self.engine.abort_request(request_id)
            raise
    
    async def get_model_config(self) -> Dict[str, Any]:
        """Get model configuration"""
        if not self.running:
            await self.start_background_loop()
        
        while self.engine is None:
            await asyncio.sleep(0.01)
        
        return {
            "model_name": self.engine.model_name,
            "tokenizer": str(type(self.engine.tokenizer).__name__),
            "vocab_size": len(self.engine.tokenizer) if self.engine.tokenizer else None
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """Check engine health"""
        return {
            "status": "healthy" if self.running else "stopped",
            "background_thread_alive": self.background_thread.is_alive() if self.background_thread else False,
            "engine_initialized": self.engine is not None
        }
    
    async def shutdown(self):
        """Shutdown the async engine"""
        logger.info("Shutting down AsyncLLMEngine")
        self.running = False
        
        if self.background_thread and self.background_thread.is_alive():
            self.background_thread.join(timeout=5.0)
        
        # Cancel all pending requests
        for request_id in list(self.request_tracker.pending_requests.keys()):
            self.request_tracker.cancel_request(request_id)
        
        logger.info("AsyncLLMEngine shutdown complete")
