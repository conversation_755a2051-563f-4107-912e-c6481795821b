"""
OpenAI-compatible protocol definitions for Mini-vLLM API server
Based on OpenAI's Chat Completions API format
"""
from typing import List, Optional, Union, Dict, Any, Literal
from pydantic import BaseModel, Field
import time


# Request Models
class ChatMessage(BaseModel):
    role: Literal["system", "user", "assistant"]
    content: str


class ChatCompletionRequest(BaseModel):
    model: str
    messages: List[ChatMessage]
    temperature: Optional[float] = 1.0
    top_p: Optional[float] = 1.0
    max_tokens: Optional[int] = 100
    stream: Optional[bool] = False
    stop: Optional[Union[str, List[str]]] = None
    
    # Additional Mini-vLLM specific parameters
    top_k: Optional[int] = -1


class CompletionRequest(BaseModel):
    model: str
    prompt: Union[str, List[str]]
    temperature: Optional[float] = 1.0
    top_p: Optional[float] = 1.0
    max_tokens: Optional[int] = 100
    stream: Optional[bool] = False
    stop: Optional[Union[str, List[str]]] = None
    top_k: Optional[int] = -1


# Response Models
class UsageInfo(BaseModel):
    prompt_tokens: int
    completion_tokens: int
    total_tokens: int


class ChatCompletionResponseChoice(BaseModel):
    index: int
    message: ChatMessage
    finish_reason: Optional[str] = None


class ChatCompletionResponse(BaseModel):
    id: str
    object: str = "chat.completion"
    created: int = Field(default_factory=lambda: int(time.time()))
    model: str
    choices: List[ChatCompletionResponseChoice]
    usage: Optional[UsageInfo] = None


# Streaming Response Models
class DeltaMessage(BaseModel):
    role: Optional[str] = None
    content: Optional[str] = None


class ChatCompletionStreamChoice(BaseModel):
    index: int
    delta: DeltaMessage
    finish_reason: Optional[str] = None


class ChatCompletionStreamResponse(BaseModel):
    id: str
    object: str = "chat.completion.chunk"
    created: int = Field(default_factory=lambda: int(time.time()))
    model: str
    choices: List[ChatCompletionStreamChoice]


# Completion Response Models
class CompletionResponseChoice(BaseModel):
    index: int
    text: str
    finish_reason: Optional[str] = None


class CompletionResponse(BaseModel):
    id: str
    object: str = "text_completion"
    created: int = Field(default_factory=lambda: int(time.time()))
    model: str
    choices: List[CompletionResponseChoice]
    usage: Optional[UsageInfo] = None


class CompletionStreamChoice(BaseModel):
    index: int
    text: str
    finish_reason: Optional[str] = None


class CompletionStreamResponse(BaseModel):
    id: str
    object: str = "text_completion"
    created: int = Field(default_factory=lambda: int(time.time()))
    model: str
    choices: List[CompletionStreamChoice]


# Model List Response
class ModelInfo(BaseModel):
    id: str
    object: str = "model"
    created: int = Field(default_factory=lambda: int(time.time()))
    owned_by: str = "mini-vllm"


class ModelListResponse(BaseModel):
    object: str = "list"
    data: List[ModelInfo]


# Error Response
class ErrorDetail(BaseModel):
    message: str
    type: str
    code: Optional[str] = None


class ErrorResponse(BaseModel):
    error: ErrorDetail


# Health Check Response
class HealthResponse(BaseModel):
    status: str = "healthy"
    model: Optional[str] = None
    version: str = "mini-vllm-0.1.0"


# Utility functions for converting between formats
def create_chat_completion_response(
    request_id: str,
    model: str,
    message_content: str,
    finish_reason: str = "stop",
    usage: Optional[UsageInfo] = None
) -> ChatCompletionResponse:
    """Create a chat completion response"""
    return ChatCompletionResponse(
        id=request_id,
        model=model,
        choices=[
            ChatCompletionResponseChoice(
                index=0,
                message=ChatMessage(role="assistant", content=message_content),
                finish_reason=finish_reason
            )
        ],
        usage=usage
    )


def create_chat_completion_stream_chunk(
    request_id: str,
    model: str,
    delta_content: Optional[str] = None,
    finish_reason: Optional[str] = None,
    role: Optional[str] = None
) -> ChatCompletionStreamResponse:
    """Create a streaming chat completion chunk"""
    return ChatCompletionStreamResponse(
        id=request_id,
        model=model,
        choices=[
            ChatCompletionStreamChoice(
                index=0,
                delta=DeltaMessage(role=role, content=delta_content),
                finish_reason=finish_reason
            )
        ]
    )


def create_error_response(message: str, error_type: str = "invalid_request") -> ErrorResponse:
    """Create an error response"""
    return ErrorResponse(
        error=ErrorDetail(
            message=message,
            type=error_type
        )
    )


def messages_to_prompt(messages: List[ChatMessage]) -> str:
    """Convert chat messages to a single prompt string"""
    prompt_parts = []
    
    for message in messages:
        role = message.role
        content = message.content
        
        if role == "system":
            prompt_parts.append(f"System: {content}")
        elif role == "user":
            prompt_parts.append(f"User: {content}")
        elif role == "assistant":
            prompt_parts.append(f"Assistant: {content}")
    
    # Add assistant prompt for generation
    prompt_parts.append("Assistant:")
    
    return "\n".join(prompt_parts)


def calculate_usage(prompt_tokens: int, completion_tokens: int) -> UsageInfo:
    """Calculate token usage information"""
    return UsageInfo(
        prompt_tokens=prompt_tokens,
        completion_tokens=completion_tokens,
        total_tokens=prompt_tokens + completion_tokens
    )
