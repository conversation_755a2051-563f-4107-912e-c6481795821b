"""
Mini-vLLM: A simplified, educational implementation of vLLM's core architecture

This package provides a minimal implementation of the core vLLM architecture:
- LLM interface for easy usage
- Engine for request coordination  
- Scheduler for batching and queue management
- ModelRunner for inference using HuggingFace transformers
- OpenAI-compatible API server with streaming support

Key simplifications compared to full vLLM:
- Single GPU/CPU only (no distributed execution)
- Basic batching (no advanced memory management)
- HuggingFace transformers (no custom kernels)
- Simple scheduling (FIFO, no priorities)
- Educational focus over production optimization

Example usage:

Basic inference:
    >>> from mini_vllm import MiniLLM, SamplingParams
    >>> llm = MiniLLM("gpt2")
    >>> prompts = ["The future of AI is", "Once upon a time"]
    >>> sampling_params = SamplingParams(temperature=0.8, max_tokens=50)
    >>> outputs = llm.generate(prompts, sampling_params)
    >>> for output in outputs:
    ...     print(f"Generated: {output.text}")

API Server:
    >>> from mini_vllm.api import create_app
    >>> app = create_app({"model_name": "gpt2"})
    >>> # Run with: uvicorn app:app --host 0.0.0.0 --port 8000
"""

from mini_vllm.core.llm import MiniLLM
from mini_vllm.core.engine import MiniEngine
from mini_vllm.core.data_structures import (
    SamplingParams,
    Request,
    RequestOutput,
    Sequence,
    SequenceGroup,
    SequenceStatus,
)

# Version information
__version__ = "0.1.0"
__author__ = "Mini-vLLM Contributors"
__email__ = "<EMAIL>"

# Main exports for easy importing
__all__ = [
    # Main user interface
    "MiniLLM",
    
    # Core components
    "MiniEngine",
    
    # Data structures
    "SamplingParams",
    "Request",
    "RequestOutput",
    "Sequence",
    "SequenceGroup",
    "SequenceStatus",
    
    # Version info
    "__version__",
]

# Convenience function for quick usage
def generate_text(
    model: str,
    prompts,
    temperature: float = 1.0,
    max_tokens: int = 100,
    top_p: float = 1.0,
    **kwargs
):
    """
    Convenience function for quick text generation
    
    Args:
        model: HuggingFace model name
        prompts: Prompt(s) to generate from
        temperature: Sampling temperature
        max_tokens: Maximum tokens to generate
        top_p: Top-p (nucleus) sampling parameter
        **kwargs: Additional arguments for MiniLLM
        
    Returns:
        List of generated text strings
    """
    # Create sampling parameters
    sampling_params = SamplingParams(
        temperature=temperature,
        max_tokens=max_tokens,
        top_p=top_p
    )
    
    # Create LLM and generate
    llm = MiniLLM(model, **kwargs)
    outputs = llm.generate(prompts, sampling_params)
    
    # Extract just the text
    return [output.text for output in outputs]


# Add generate_text to exports
__all__.append("generate_text")
