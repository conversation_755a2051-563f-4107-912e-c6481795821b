"""
Core data structures for Mini-vLLM
Simplified versions of vLLM's data classes
"""
from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any
from enum import Enum
import time
import uuid


class SequenceStatus(Enum):
    """Status of a sequence during generation"""
    WAITING = "waiting"      # In queue, not yet processed
    RUNNING = "running"      # Currently being processed
    FINISHED = "finished"    # Generation complete


@dataclass
class SamplingParams:
    """Parameters for text generation sampling"""
    temperature: float = 1.0
    top_p: float = 1.0
    top_k: int = -1
    max_tokens: int = 100
    stop: Optional[List[str]] = None
    
    def __post_init__(self):
        if self.stop is None:
            self.stop = []


@dataclass
class Request:
    """A single generation request from the user"""
    request_id: str
    prompt: str
    sampling_params: SamplingParams
    arrival_time: float = field(default_factory=time.time)
    
    @classmethod
    def create(cls, prompt: str, sampling_params: SamplingParams) -> "Request":
        """Create a new request with auto-generated ID"""
        return cls(
            request_id=str(uuid.uuid4()),
            prompt=prompt,
            sampling_params=sampling_params
        )


@dataclass
class Sequence:
    """A sequence being generated (one per request in our simple version)"""
    seq_id: str
    request_id: str
    prompt: str
    prompt_token_ids: List[int]
    output_token_ids: List[int] = field(default_factory=list)
    status: SequenceStatus = SequenceStatus.WAITING
    
    @property
    def is_finished(self) -> bool:
        """Check if sequence generation is complete"""
        return self.status == SequenceStatus.FINISHED
    
    @property
    def get_len(self) -> int:
        """Get total length of sequence (prompt + output)"""
        return len(self.prompt_token_ids) + len(self.output_token_ids)
    
    def add_token(self, token_id: int):
        """Add a new generated token to the sequence"""
        self.output_token_ids.append(token_id)
    
    def get_output_text(self, tokenizer) -> str:
        """Decode output tokens to text"""
        if not self.output_token_ids:
            return ""
        return tokenizer.decode(self.output_token_ids, skip_special_tokens=True)


@dataclass
class SequenceGroup:
    """Group of sequences from the same request (simplified - just one sequence)"""
    request_id: str
    sequences: List[Sequence]
    sampling_params: SamplingParams
    arrival_time: float
    
    @classmethod
    def from_request(cls, request: Request, tokenizer) -> "SequenceGroup":
        """Create a sequence group from a request"""
        # Tokenize the prompt
        prompt_token_ids = tokenizer.encode(request.prompt)
        
        # Create a single sequence
        sequence = Sequence(
            seq_id=f"{request.request_id}_0",
            request_id=request.request_id,
            prompt=request.prompt,
            prompt_token_ids=prompt_token_ids
        )
        
        return cls(
            request_id=request.request_id,
            sequences=[sequence],
            sampling_params=request.sampling_params,
            arrival_time=request.arrival_time
        )
    
    @property
    def is_finished(self) -> bool:
        """Check if all sequences in group are finished"""
        return all(seq.is_finished for seq in self.sequences)
    
    def get_main_sequence(self) -> Sequence:
        """Get the main (and only) sequence"""
        return self.sequences[0]


@dataclass
class ModelInput:
    """Input to the model for a batch of sequences"""
    input_ids: List[List[int]]      # Batch of token sequences
    attention_mask: List[List[int]]  # Attention masks
    position_ids: List[List[int]]    # Position indices
    sequence_groups: List[SequenceGroup]  # Associated sequence groups
    
    @property
    def batch_size(self) -> int:
        return len(self.input_ids)


@dataclass
class ModelOutput:
    """Output from the model"""
    next_token_ids: List[int]       # Next token for each sequence
    logits: Optional[Any] = None    # Raw logits (if needed)
    
    @property
    def batch_size(self) -> int:
        return len(self.next_token_ids)


@dataclass
class RequestOutput:
    """Final output for a completed request"""
    request_id: str
    prompt: str
    text: str
    finished: bool
    finish_reason: Optional[str] = None
    
    def __str__(self) -> str:
        return f"RequestOutput(request_id={self.request_id}, text='{self.text[:50]}...', finished={self.finished})"


@dataclass
class SchedulerOutput:
    """Output from the scheduler about what to execute"""
    scheduled_groups: List[SequenceGroup]  # Groups to process this step
    num_batched_tokens: int                # Total tokens in batch
    
    @property
    def is_empty(self) -> bool:
        return len(self.scheduled_groups) == 0
