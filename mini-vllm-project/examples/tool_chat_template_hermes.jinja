{%- macro json_to_python_type(json_spec) %}
    {%- set basic_type_map = {
    "string": "str",
    "number": "float",
    "integer": "int",
    "boolean": "bool"
} %}

    {%- if basic_type_map[json_spec.type] is defined %}
        {{- basic_type_map[json_spec.type] }}
    {%- elif json_spec.type == "array" %}
        {{- "list[" +  json_to_python_type(json_spec|items) + "]" }}
    {%- elif json_spec.type == "object" %}
        {%- if json_spec.additionalProperties is defined %}
            {{- "dict[str, " + json_to_python_type(json_spec.additionalProperties) + ']' }}
        {%- else %}
            {{- "dict" }}
        {%- endif %}
    {%- elif json_spec.type is iterable %}
        {{- "Union[" }}
        {%- for t in json_spec.type %}
            {{- json_to_python_type({"type": t}) }}
            {%- if not loop.last %}
                {{- "," }}
            {%- endif %}
        {%- endfor %}
        {{- "]" }}
    {%- else %}
        {{- "Any" }}
    {%- endif %}
{%- endmacro %}


{{- bos_token }}
{{- "<|im_start|>system\nYou are a function calling AI model. You are provided with function signatures within <tools></tools> XML tags. You may call one or more functions to assist with the user query. Don't make assumptions about what values to plug into functions. Here are the available tools: <tools> " }}
{%- if tools is iterable and tools | length > 0 %}
    {%- for tool in tools %}
        {%- if tool.function is defined %}
            {%- set tool = tool.function %}
        {%- endif %}
        {{- '{"type": "function", "function": ' }}
        {{- '{"name": "' + tool.name + '", ' }}
        {{- '"description": "' + tool.name + '(' }}
        {%- for param_name, param_fields in tool.parameters.properties|items %}
            {{- param_name + ": " + json_to_python_type(param_fields) }}
            {%- if not loop.last %}
                {{- ", " }}
            {%- endif %}
        {%- endfor %}
        {{- ")" }}
        {%- if tool.return is defined %}
            {{- " -> " + json_to_python_type(tool.return) }}
        {%- endif %}
        {{- " - " + tool.description + "\n\n" }}
        {%- for param_name, param_fields in tool.parameters.properties|items %}
            {%- if loop.first %}
                {{- "    Args:\n" }}
            {%- endif %}
            {{- "        " + param_name + "(" + json_to_python_type(param_fields) + "): " + param_fields.description|trim }}
        {%- endfor %}
        {%- if tool.return is defined and tool.return.description is defined %}
            {{- "\n    Returns:\n        " + tool.return.description }}
        {%- endif %}
        {{- '"' }}
        {{- ', "parameters": ' }}
        {%- if tool.parameters.properties | length == 0 %}
            {{- "{}" }}
        {%- else %}
            {{- tool.parameters|tojson }}
        {%- endif %}
        {{- "}" }}
        {%- if not loop.last %}
            {{- "\n" }}
        {%- endif %}
    {%- endfor %}
{%- endif %}
{{- " </tools>" }}
{{- 'Use the following pydantic model json schema for each tool call you will make: {"properties": {"name": {"title": "Name", "type": "string"}, "arguments": {"title": "Arguments", "type": "object"}}, "required": ["name", "arguments"], "title": "FunctionCall", "type": "object"}}
' }}
{{- "For each function call return a json object with function name and arguments within <tool_call></tool_call> XML tags as follows:
" }}
{{- "<tool_call>
" }}
{{- '{"name": <function-name>, "arguments": <args-dict>}
' }}
{{- '</tool_call><|im_end|>' }}
{%- for message in messages %}
    {%- if message.role == "user" or message.role == "system" or (message.role == "assistant" and message.tool_calls is not defined) %}
        {{- '<|im_start|>' + message.role + '\n' + message.content + '<|im_end|>' + '\n' }}
    {%- elif message.role == "assistant" and message.tool_calls is defined %}
        {{- '<|im_start|>' + message.role }}
        {%- for tool_call in message.tool_calls %}
            {{- '\n<tool_call>\n' }}
            {%- if tool_call.function is defined %}
                {%- set tool_call = tool_call.function %}
            {%- endif %}
            {{- '{' }}
            {{- '"name": "' }}
            {{- tool_call.name }}
            {{- '"' }}
            {%- if tool_call.arguments is defined %}
                {{- ', ' }}
                {{- '"arguments": ' }}
                {{- tool_call.arguments|tojson }}
            {%- endif %}
            {{- '}' }}
            {{- '\n</tool_call>' }}
        {%- endfor %}
        {{- '<|im_end|>\n' }}
    {%- elif message.role == "tool" %}
        {%- if loop.previtem and loop.previtem.role != "tool" %}
            {{- '<|im_start|>tool\n' }}
        {%- endif %}
        {{- '<tool_response>\n' }}
        {{- message.content }}
        {%- if not loop.last %}
            {{- '\n</tool_response>\n' }}
        {%- else %}
            {{- '\n</tool_response>' }}
        {%- endif %}
        {%- if not loop.last and loop.nextitem.role != "tool" %}
            {{- '<|im_end|>' }}
        {%- elif loop.last %}
            {{- '<|im_end|>' }}
        {%- endif %}
    {%- endif %}
{%- endfor %}
{%- if add_generation_prompt %}
    {{- '<|im_start|>assistant\n' }}
{%- endif %}
