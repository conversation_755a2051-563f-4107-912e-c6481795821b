{%- for message in messages -%}
    {%- if message['role'] == 'user' -%}
        {{- 'User: ' + message['content'] -}}
    {%- elif message['role'] == 'assistant' -%}
        {{- 'Assistant: ' + message['content'] -}}
    {%- endif -%}
    {%- if (loop.last and add_generation_prompt) or not loop.last -%}
        {{- '\n' -}}
    {%- endif -%}
{%- endfor -%}


{%- if add_generation_prompt and messages[-1]['role'] != 'assistant' -%}
    {{- 'Assistant:' -}}
{% endif %}