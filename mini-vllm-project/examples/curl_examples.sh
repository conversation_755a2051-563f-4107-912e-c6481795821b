#!/bin/bash
# Mini-vLLM API Examples using curl
# Make sure the server is running: python serve_api.py

API_BASE="http://localhost:8000"
MODEL="gpt2"

echo "🚀 Mini-vLLM API Examples with curl"
echo "=================================="

# Health check
echo ""
echo "📊 Health Check:"
curl -s "$API_BASE/health" | jq '.'

# List models
echo ""
echo "📋 List Models:"
curl -s "$API_BASE/v1/models" | jq '.'

# Chat completion (non-streaming)
echo ""
echo "💬 Chat Completion (Non-streaming):"
curl -s "$API_BASE/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "'$MODEL'",
    "messages": [
      {"role": "system", "content": "You are a helpful assistant."},
      {"role": "user", "content": "What is the capital of France?"}
    ],
    "max_tokens": 50,
    "temperature": 0.7
  }' | jq '.'

# Text completion
echo ""
echo "📝 Text Completion:"
curl -s "$API_BASE/v1/completions" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "'$MODEL'",
    "prompt": "The future of AI is",
    "max_tokens": 30,
    "temperature": 0.8
  }' | jq '.'

# Chat completion (streaming)
echo ""
echo "🌊 Chat Completion (Streaming):"
echo "Note: Streaming output will appear in real-time"
curl -s "$API_BASE/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "'$MODEL'",
    "messages": [
      {"role": "user", "content": "Tell me a joke"}
    ],
    "max_tokens": 50,
    "temperature": 0.9,
    "stream": true
  }' | while IFS= read -r line; do
    if [[ $line == data:* ]]; then
      data=${line#data: }
      if [[ $data != "[DONE]" ]]; then
        echo "$data" | jq -r '.choices[0].delta.content // empty' | tr -d '\n'
      fi
    fi
  done

echo ""
echo ""
echo "✅ All curl examples completed!"
