#!/usr/bin/env python3
"""
Example clients for Mini-vLLM API Server
Demonstrates how to use the OpenAI-compatible API with both streaming and non-streaming
"""
import asyncio
import json
import time
from typing import Dict, Any, AsyncGenerator

import httpx
import aiohttp


# Configuration
API_BASE = "http://localhost:8000"
MODEL_NAME = "gpt2"  # Should match the model loaded in the server


class MiniVLLMClient:
    """Simple client for Mini-vLLM API"""
    
    def __init__(self, base_url: str = API_BASE):
        self.base_url = base_url.rstrip("/")
        self.client = httpx.AsyncClient(timeout=60.0)
    
    async def chat_completion(
        self,
        messages: list,
        model: str = MODEL_NAME,
        temperature: float = 0.7,
        max_tokens: int = 100,
        stream: bool = False
    ) -> Dict[str, Any]:
        """Create a chat completion"""
        url = f"{self.base_url}/v1/chat/completions"
        
        payload = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens,
            "stream": stream
        }
        
        if stream:
            return await self._stream_chat_completion(url, payload)
        else:
            response = await self.client.post(url, json=payload)
            response.raise_for_status()
            return response.json()
    
    async def _stream_chat_completion(self, url: str, payload: Dict[str, Any]):
        """Handle streaming chat completion"""
        async with self.client.stream("POST", url, json=payload) as response:
            response.raise_for_status()
            
            full_content = ""
            async for line in response.aiter_lines():
                if line.startswith("data: "):
                    data = line[6:]  # Remove "data: " prefix
                    
                    if data == "[DONE]":
                        break
                    
                    try:
                        chunk = json.loads(data)
                        delta = chunk["choices"][0]["delta"]
                        
                        if "content" in delta and delta["content"]:
                            content = delta["content"]
                            full_content += content
                            print(content, end="", flush=True)
                    
                    except json.JSONDecodeError:
                        continue
            
            print()  # New line after streaming
            return {"content": full_content}
    
    async def text_completion(
        self,
        prompt: str,
        model: str = MODEL_NAME,
        temperature: float = 0.7,
        max_tokens: int = 100
    ) -> Dict[str, Any]:
        """Create a text completion"""
        url = f"{self.base_url}/v1/completions"
        
        payload = {
            "model": model,
            "prompt": prompt,
            "temperature": temperature,
            "max_tokens": max_tokens
        }
        
        response = await self.client.post(url, json=payload)
        response.raise_for_status()
        return response.json()
    
    async def list_models(self) -> Dict[str, Any]:
        """List available models"""
        url = f"{self.base_url}/v1/models"
        response = await self.client.get(url)
        response.raise_for_status()
        return response.json()
    
    async def health_check(self) -> Dict[str, Any]:
        """Check server health"""
        url = f"{self.base_url}/health"
        response = await self.client.get(url)
        response.raise_for_status()
        return response.json()
    
    async def close(self):
        """Close the client"""
        await self.client.aclose()


async def example_chat_completion():
    """Example: Chat completion (non-streaming)"""
    print("🤖 Example: Chat Completion (Non-streaming)")
    print("=" * 50)
    
    client = MiniVLLMClient()
    
    try:
        messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "What is the capital of France?"}
        ]
        
        print("Messages:")
        for msg in messages:
            print(f"  {msg['role']}: {msg['content']}")
        
        print("\nResponse:")
        response = await client.chat_completion(messages, temperature=0.7, max_tokens=50)
        
        assistant_message = response["choices"][0]["message"]["content"]
        print(f"  assistant: {assistant_message}")
        
        # Print usage info
        if "usage" in response:
            usage = response["usage"]
            print(f"\nUsage: {usage['prompt_tokens']} prompt + {usage['completion_tokens']} completion = {usage['total_tokens']} total tokens")
    
    finally:
        await client.close()


async def example_chat_completion_streaming():
    """Example: Chat completion with streaming"""
    print("\n🌊 Example: Chat Completion (Streaming)")
    print("=" * 50)
    
    client = MiniVLLMClient()
    
    try:
        messages = [
            {"role": "system", "content": "You are a creative storyteller."},
            {"role": "user", "content": "Tell me a short story about a robot."}
        ]
        
        print("Messages:")
        for msg in messages:
            print(f"  {msg['role']}: {msg['content']}")
        
        print("\nStreaming response:")
        print("  assistant: ", end="")
        
        await client.chat_completion(messages, temperature=0.8, max_tokens=100, stream=True)
    
    finally:
        await client.close()


async def example_text_completion():
    """Example: Text completion"""
    print("\n📝 Example: Text Completion")
    print("=" * 50)
    
    client = MiniVLLMClient()
    
    try:
        prompt = "The future of artificial intelligence is"
        
        print(f"Prompt: {prompt}")
        
        response = await client.text_completion(prompt, temperature=0.7, max_tokens=50)
        
        completion = response["choices"][0]["text"]
        print(f"Completion: {completion}")
        
        # Print usage info
        if "usage" in response:
            usage = response["usage"]
            print(f"\nUsage: {usage['prompt_tokens']} prompt + {usage['completion_tokens']} completion = {usage['total_tokens']} total tokens")
    
    finally:
        await client.close()


async def example_server_info():
    """Example: Get server information"""
    print("\n📊 Example: Server Information")
    print("=" * 50)
    
    client = MiniVLLMClient()
    
    try:
        # Health check
        health = await client.health_check()
        print(f"Health: {health}")
        
        # List models
        models = await client.list_models()
        print(f"\nAvailable models:")
        for model in models["data"]:
            print(f"  - {model['id']} (owned by {model['owned_by']})")
    
    finally:
        await client.close()


async def example_openai_compatible():
    """Example: Using with OpenAI Python client (if available)"""
    print("\n🔗 Example: OpenAI Python Client Compatibility")
    print("=" * 50)
    
    try:
        # Try to import OpenAI client
        from openai import AsyncOpenAI
        
        # Create client pointing to our server
        client = AsyncOpenAI(
            api_key="dummy-key",  # Mini-vLLM doesn't require auth by default
            base_url=f"{API_BASE}/v1"
        )
        
        # Chat completion
        response = await client.chat.completions.create(
            model=MODEL_NAME,
            messages=[
                {"role": "user", "content": "Hello, how are you?"}
            ],
            max_tokens=50,
            temperature=0.7
        )
        
        print(f"OpenAI client response: {response.choices[0].message.content}")
        
    except ImportError:
        print("OpenAI Python client not installed. Install with: pip install openai")
    except Exception as e:
        print(f"Error with OpenAI client: {e}")


async def main():
    """Run all examples"""
    print("🚀 Mini-vLLM API Client Examples")
    print("Make sure the API server is running on http://localhost:8000")
    print("Start it with: python serve_api.py")
    
    # Wait a moment for user to read
    await asyncio.sleep(2)
    
    try:
        await example_server_info()
        await example_chat_completion()
        await example_chat_completion_streaming()
        await example_text_completion()
        await example_openai_compatible()
        
        print("\n✅ All examples completed successfully!")
        
    except httpx.ConnectError:
        print("\n❌ Could not connect to API server.")
        print("Make sure the server is running: python serve_api.py")
    except Exception as e:
        print(f"\n❌ Error running examples: {e}")


if __name__ == "__main__":
    asyncio.run(main())
